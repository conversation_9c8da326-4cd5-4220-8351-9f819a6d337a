<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Analytics</title>
    {% include 'imports.html' %}
    <script>
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
</head>

<body class="bg-background text-foreground">
    {% include 'components/loading.html' %}

    <div class="grid min-h-screen w-full lg:grid-cols-[280px_1fr]">
        {% include 'sidebar.html' %}

        <div class="flex flex-col">
            <header
                class="card flex h-14 lg:h-[60px] items-center justify-between gap-4 border-b card px-4 sticky-page-header">
                <div style="margin-left: 8px;" class="flex items-center gap-2 px-4 pl-0">
                    <button id="toggle-btn" class="opacity-100 transition-opacity duration-300 focus:outline-none"
                        style="background-color: transparent !important;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-panel-left">
                            <rect width="18" height="18" x="3" y="3" rx="2"></rect>
                            <path d="M9 3v18"></path>
                        </svg>
                    </button>
                    <div data-orientation="vertical" role="none" class="shrink-0 bg-border w-[1px] mr-3 h-4"
                        style="background-color: var(--border-color);"></div>
                    <nav aria-label="breadcrumb">
                        <ol
                            class="flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5">

                            <!-- Sales Report link -->
                            <div class="menubar" role="menubar">
                                <div class="menubar-indicator"></div>
                                <a href="/sales" role="menuitem">Sales Report</a>
                                <a href="/pmsanalytics" role="menuitem">PMS Analytics</a>
                                <a href="/googleana" role="menuitem">Google Analytics</a>
                                <a href="/status" role="menuitem">Status Report</a>
                                <a href="/staff-analytics" role="menuitem" class="active">Staff Analytics</a>
                            </div>
                        </ol>
                    </nav>
                </div>
                {% include 'topright.html' %}
            </header>
            <main class="flex flex-1 flex-col gap-4 p-4 md:gap-4 md:p-6 card">
                <!-- Total Data containers -->
                <div class="mb-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-9 gap-4">
                        <!-- Card 1: Total Chats Handled -->
                        <div class="col-span-1 lg:col-span-2 card rounded-lg border shadow-sm flex flex-col">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Total Chats Handled</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-messages-square">
                                    <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2z"></path>
                                    <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1"></path>
                                </svg>
                            </div>
                            <div class="p-6 pt-0 mt-auto">
                                <div class="text-2xl font-bold mt-1 ">1,234</div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Total chats handled by all staff</p>
                            </div>
                        </div>

                        <!-- Card 2: Active Chat Sessions -->
                        <div class="col-span-1 lg:col-span-2 card rounded-lg border shadow-sm flex flex-col">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Active Chat Sessions</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-check">
                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="9" cy="7" r="4"></circle>
                                    <polyline points="16 11 18 13 22 9"></polyline>
                                </svg>
                            </div>
                            <div class="p-6 pt-0 mt-auto">
                                <div class="text-2xl font-bold mt-1 ">15</div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Currently active chat sessions</p>
                            </div>
                        </div>

                        <!-- Card 3: Average Response Time -->
                        <div class="col-span-1 lg:col-span-2 card rounded-lg border shadow-sm flex flex-col">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Avg. Response Time</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12 6 12 12 16 14"></polyline>
                                </svg>
                            </div>
                            <div class="p-6 pt-0 mt-auto">
                                <div class="text-2xl font-bold mt-1 ">3m 15s</div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Average time to first response</p>
                            </div>
                        </div>

                        <!-- Card 4: Task Priority Distribution (Moved here) -->
                        <div class="col-span-1 lg:col-span-3 card rounded-lg border shadow-sm">
                            <div class="p-6 flex flex-row items-center justify-between pb-2 space-y-0">
                                <div class="flex items-center gap-2">
                                    <h3 class="whitespace-nowrap tracking-tight text-sm font-medium">Task Priority Distribution</h3>
                                    <div class="group relative">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-black text-white text-xs rounded py-1 px-2 hidden group-hover:block w-48 text-center">
                                            Distribution of tasks handled by priority.
                                        </div>
                                    </div>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-list-filter">
                                    <path d="M3 6h18" />
                                    <path d="M7 12h10" />
                                    <path d="M10 18h4" />
                                </svg>
                            </div>
                            <div class="p-6">
                                <div id="taskPriorityProgressBar" style="margin-bottom: 5px;" class="mt-3 flex h-1.5 w-full rounded-full bg-gray-100 h-[10px] overflow-hidden">
                                </div>
                                <div id="taskPriorityLegend" class="mt-2 text-sm"></div>
                            </div>
                        </div>

                        <!-- More cards for Chat Performance will be added here -->
                    </div>
                </div>

                <!-- Task Management Metrics -->
                <div class="mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">

                        <!-- Row: Peak Activity Hours (wide) + Task Categories Handled (narrow) -->
                        <div class="col-span-1 md:col-span-2 lg:col-span-4">
                            <div class="card rounded-lg border shadow-sm h-full flex flex-col">
                                <div class="flex justify-between items-center p-4 border-b card">
                                    <div class="flex flex-col">
                                        <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">
                                            Peak Activity Hours</h3>
                                        <p class="text-sm text-muted-foreground mt-1">Staff activity throughout the day</p>
                                    </div>
                                    <button id="peakActivityFilterBtn" class="uk-button border card default" type="button">
                                        Today
                                        <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"></path>
                                        </svg>
                                    </button>
                                    <div class="uk-drop uk-dropdown dropdown-content" uk-dropdown="mode: click; pos: bottom-right">
                                        <ul class="uk-dropdown-nav uk-nav">
                                            <li><a href="#" class="peak-activity-option">Today</a></li>
                                            <li><a href="#" class="peak-activity-option">Last 7 Days</a></li>
                                            <li><a href="#" class="peak-activity-option">Last 30 Days</a></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="px-4 pb-1 pt-0 relative" style="height:235px;">
                                    <canvas id="peakActivityChart" style="width: 100%; height: 100%;"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1 md:col-span-2 lg:col-span-2">
                            <div class="card rounded-lg border shadow-sm h-full flex flex-col">
                                <div class="p-4 flex justify-between items-center border-b card">
                                    <div class="pr-6">
                                        <h3 class="whitespace-nowrap text-lg font-semibold leading-none tracking-tight">Task Categories Handled</h3>
                                        <p class="text-sm text-muted-foreground mt-1">Distribution of tasks by category</p>
                                    </div>
                                    <!-- Optional: Add a filter button if needed later -->
                                </div>
                                <div class="p-4 pt-0 relative" style="height:235px;">
                                    <div id="taskCategoriesChartDiv" style="width: 100%; height: 100%;"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Total Data containers end -->
                    </div>
                </div>
                </div>
                <!-- Other KPI sections will be added below -->
                <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
                <script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
                <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
                <script src="/static/js/shadcntooltip.js"></script>
                <script>
                    am4core.ready(function () {
                        am4core.useTheme(am4themes_animated);

                        var taskCatChart = am4core.create("taskCategoriesChartDiv", am4charts.PieChart);
                        taskCatChart.radius = am4core.percent(60);

                        taskCatChart.data = [
                            { category: "Food & Bev", count: 150, color: am4core.color("#e76e51") },
                            { category: "Housekeeping", count: 120, color: am4core.color("#2c9b91") },
                            { category: "Maintenance", count: 80, color: am4core.color("#f2a262") },
                            { category: "Guest Request", count: 102, color: am4core.color("#e7c468") },
                            { category: "Other", count: 50, color: am4core.color("#264754") }
                        ];

                        var taskCatPieSeries = taskCatChart.series.push(new am4charts.PieSeries());
                        taskCatPieSeries.dataFields.value = "count";
                        taskCatPieSeries.dataFields.category = "category";
                        taskCatPieSeries.slices.template.propertyFields.fill = "color";
                        taskCatChart.logo.disabled = true;
                        taskCatChart.innerRadius = am4core.percent(30);
                        taskCatPieSeries.slices.template.padding = 1;
                        taskCatPieSeries.slices.template.cornerRadius = 5;
                        taskCatPieSeries.slices.template.fillOpacity = 1;
                        taskCatPieSeries.slices.template.strokeWidth = 0;
                        taskCatPieSeries.slices.template.stroke = am4core.color("#ffffff");
                        taskCatPieSeries.labels.template.disabled = false;
                        taskCatPieSeries.labels.template.text = "{category}: {value.value}";
                        taskCatPieSeries.labels.template.radius = 1;
                        taskCatPieSeries.labels.template.fontSize = 12;
                        taskCatPieSeries.labels.template.maxWidth = 70;
                        taskCatPieSeries.labels.template.wrap = true;
                        taskCatPieSeries.ticks.template.disabled = false;
                        taskCatPieSeries.ticks.template.strokeOpacity = 0.7;
                        taskCatPieSeries.ticks.template.strokeWidth = 2;
                        taskCatPieSeries.ticks.template.length = 15;
                        taskCatPieSeries.slices.template.tooltipText = "{category}: {value.value} tasks";

                        function updateTaskCategoryChartColors() {
                            var body = document.body;
                            var isDarkTheme = body.classList.contains('pure-black') || body.classList.contains('dark-gray') || body.classList.contains('navy-blue') || body.classList.contains('cool-blue') || body.classList.contains('deep-burgundy') || body.classList.contains('charcoal');
                            taskCatPieSeries.labels.template.fill = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                            taskCatPieSeries.ticks.template.stroke = am4core.color(isDarkTheme ? "#ecf0f1" : "#34495e");
                        }
                        updateTaskCategoryChartColors();
                        var taskCatObserver = new MutationObserver(function (mutations) {
                            mutations.forEach(function (mutation) {
                                if (mutation.type === "attributes" && mutation.attributeName === "class") {
                                    updateTaskCategoryChartColors();
                                }
                            });
                        });
                        taskCatObserver.observe(document.body, { attributes: true });
                    });

                    document.addEventListener('DOMContentLoaded', function () {
                        // Task Priority Progress Bar
                        const priorityData = [
                            { name: 'High', value: 80, color: 'bg-red-500' },
                            { name: 'Medium', value: 150, color: 'bg-yellow-500' },
                            { name: 'Low', value: 222, color: 'bg-green-500' },
                        ];
                        const totalPriority = priorityData.reduce((sum, p) => sum + p.value, 0);
                        const priorityContainer = document.getElementById('taskPriorityProgressBar');
                        const priorityLegendContainer = document.getElementById('taskPriorityLegend');
                        priorityContainer.innerHTML = '';
                        priorityLegendContainer.innerHTML = '';
                        priorityLegendContainer.className = 'flex flex-row flex-wrap justify-center mt-2 text-[12px]';
                        priorityData.forEach(item => {
                            const barWidth = ((item.value / totalPriority) * 100).toFixed(2);
                            const barDiv = document.createElement('div');
                            barDiv.className = `h-full ${item.color}`;
                            barDiv.style.width = barWidth + '%';
                            priorityContainer.appendChild(barDiv);
                            const legendLabel = document.createElement('div');
                            legendLabel.className = 'flex items-center gap-1 mr-3';
                            legendLabel.innerHTML = `<span class="inline-block w-2.5 h-2.5 rounded-sm ${item.color}"></span> <span class="text-xs">${item.name}: ${item.value}</span>`;
                            priorityLegendContainer.appendChild(legendLabel);
                        });

                        // Peak Activity Chart
                        let peakActivityChartInstance = null;
                        function initializePeakActivityChart(filter = 'Today') {
                            const activityData = {
                                'Today': [5, 8, 12, 15, 22, 18, 10, 7, 9, 16, 20, 14, 11, 6, 4, 3, 2, 1, 1, 2, 3, 5, 6, 4], // 24 hours
                                'Last 7 Days': [60, 75, 90, 110, 150, 130, 80, 65, 70, 100, 140, 100, 85, 50, 40, 30, 25, 20, 22, 28, 35, 45, 50, 38],
                                'Last 30 Days': [250, 300, 380, 450, 600, 550, 350, 280, 320, 420, 580, 400, 360, 220, 180, 150, 120, 100, 110, 130, 160, 200, 230, 170]
                            };
                            const labels = Array.from({ length: 24 }, (_, i) => `${String(i).padStart(2, '0')}:00`);
                            const data = activityData[filter];

                            const isPureBlack = document.body.classList.contains('pure-black');
                            const chartBarColor = isPureBlack ? '#fafafa' : '#18181b';
                            const gridColor = isPureBlack ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
                            const labelColor = isPureBlack ? '#a1a1aa' : '#555555';


                            const chartData = {
                                labels: labels,
                                datasets: [{
                                    data: data,
                                    backgroundColor: chartBarColor,
                                    borderColor: chartBarColor,
                                    borderWidth: 0,
                                    borderRadius: { topLeft: 5, topRight: 5, bottomLeft: 5, bottomRight: 5 },
                                    borderSkipped: false
                                }]
                            };
                            const ctx = document.getElementById('peakActivityChart').getContext('2d');
                            if (peakActivityChartInstance) {
                                peakActivityChartInstance.destroy();
                            }
                            peakActivityChartInstance = new Chart(ctx, {
                                type: 'bar',
                                data: chartData,
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    layout: { padding: { bottom: 0, top: 20 } }, // Adjusted padding
                                    barPercentage: 1,
                                    categoryPercentage: 0.7,
                                    devicePixelRatio: 2,
                                    scales: {
                                        x: {
                                            grid: { display: false, color: 'transparent' },
                                            border: { display: false },
                                            ticks: { color: labelColor, autoSkip: true, maxTicksLimit: 12 }
                                        },
                                        y: {
                                            beginAtZero: true,
                                            grid: { color: gridColor, borderDash: [5, 5] },
                                            border: { display: false },
                                            ticks: { display: false }
                                        }
                                    },
                                    plugins: {
                                        legend: { display: false },
                                        tooltip: {
                                            enabled: false, // Disable the default Chart.js tooltip
                                            external: createCustomTooltipForTips // Use your custom tooltip function
                                        }
                                    }
                                }
                            });
                        }
                        initializePeakActivityChart(); // Initial chart

                        // Dropdown logic for Peak Activity Chart
                        const peakActivityFilterBtn = document.getElementById('peakActivityFilterBtn');
                        const peakActivityOptions = document.querySelectorAll('.peak-activity-option');
                        const peakActivityDropdownElement = peakActivityFilterBtn.nextElementSibling; // Assumes dropdown is next sibling
                        
                        if (peakActivityDropdownElement) {
                             const peakActivityDropdown = UIkit.dropdown(peakActivityDropdownElement);
                            peakActivityOptions.forEach(option => {
                                option.addEventListener('click', function (event) {
                                    event.preventDefault();
                                    const selectedFilter = this.textContent.trim();
                                    peakActivityFilterBtn.childNodes[0].nodeValue = selectedFilter + " ";
                                    initializePeakActivityChart(selectedFilter);
                                    peakActivityDropdown.hide(false);
                                });
                            });
                        }


                        // Theme change observer for Peak Activity Chart
                        const peakActivityObserver = new MutationObserver(function (mutations) {
                            mutations.forEach(function (mutation) {
                                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                                    const currentFilter = peakActivityFilterBtn.childNodes[0].nodeValue.trim();
                                    setTimeout(() => { // Ensure theme classes are fully applied
                                       initializePeakActivityChart(currentFilter);
                                    },100);
                                }
                            });
                        });
                        peakActivityObserver.observe(document.body, { attributes: true });
                    });
                </script>
            </main>
        </div>
    </div>
</body>
</html>
